////
////  PersonalHomepageViewController.swift
////  Shuxia<PERSON>qi
////
////  Created by yongsheng ye on 2025/3/24.
////
//
//  个人主页
//
import UIKit

class PersonalHomepageViewController: BaseViewController {
    var userId: String = ""
    var childVCs: [PageContainScrollView] = []
    private var collectionView: PageScrollView!
    private var cvcContentView: CollectionViewCellContentView!
    private var headerView: PageHeaderView?
    private var navigationView: UIView!
    private var navTitleLabel: UILabel!
    private var moreButton: UIButton!
    
    private var coordinator: NestedScrollCoordinator!
    
    // 新增：个人主页信息数据
    var personHomeInfo: PersonalHomeInfoResponse?

    // 新增：各个tab的实际总数
    private var worksTotal: Int = 0
    private var likeTotal: Int = 0
    private var collectTotal: Int = 0
    
    private var navigationViewHeight: CGFloat {
        return UIWindow.statusBarHeight + 54
    }
    
    private var headerViewHeight: CGFloat {
        // 如果headerView已经创建并且有数据，使用动态计算的高度
        if let headerView = collectionView.supplementaryView(forElementKind: UICollectionView.elementKindSectionHeader, at: IndexPath(item: 0, section: 0)) as? PageHeaderView {
            return headerView.calculateRequiredHeight(for: view.width)
        }
        // 否则返回默认高度
        return 490
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupCoordinator()
        // 新增：请求个人主页信息
        fetchPersonalHomeInfo()
        // 新增：请求各个tab的总数
        fetchAllTabCounts()
        // 新增：监听@用户名点击通知
        setupNotificationObserver()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        // 立即重置渐变遮罩，不使用异步调用避免闪动
        headerView?.resetGradientOverlay()

        // 重新注册通知监听，确保只有当前可见的控制器监听通知
        setupNotificationObserver()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 视图完全显示后再次确保遮罩正确
        headerView?.ensureGradientOverlayVisible()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // 移除通知监听，防止不可见的控制器响应通知
        removeNotificationObserver()
    }

    deinit {
        // 移除通知监听
        NotificationCenter.default.removeObserver(self)
    }

    /// 设置通知监听
    private func setupNotificationObserver() {
        // 先移除可能存在的监听器，避免重复注册
        removeNotificationObserver()

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleUserProfileNavigation(_:)),
            name: NSNotification.Name("NavigateToUserProfile"),
            object: nil
        )

        // 监听用户信息更新通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleUserInfoUpdated),
            name: NSNotification.Name("UserInfoUpdatedNotification"),
            object: nil
        )

        // 监听私密账号通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePrivateAccountStats),
            name: NSNotification.Name("HideStatsForPrivateAccount"),
            object: nil
        )

        // 监听公开账号通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePublicAccountStats),
            name: NSNotification.Name("ShowStatsForPublicAccount"),
            object: nil
        )

        // 监听各个tab的实际数量更新通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleWorksCountUpdate(_:)),
            name: NSNotification.Name("UpdateWorksCount"),
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleLikeCountUpdate(_:)),
            name: NSNotification.Name("UpdateLikeCount"),
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleCollectCountUpdate(_:)),
            name: NSNotification.Name("UpdateCollectCount"),
            object: nil
        )
    }

    /// 移除通知监听
    private func removeNotificationObserver() {
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("NavigateToUserProfile"), object: nil)
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("UserInfoUpdatedNotification"), object: nil)
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("HideStatsForPrivateAccount"), object: nil)
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("ShowStatsForPublicAccount"), object: nil)
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("UpdateWorksCount"), object: nil)
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("UpdateLikeCount"), object: nil)
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("UpdateCollectCount"), object: nil)
    }

    /// 处理@用户名点击跳转
    @objc private func handleUserProfileNavigation(_ notification: Notification) {
        guard let userId = notification.object as? String, !userId.isEmpty else {
            print("[PersonalHomepageViewController] handleUserProfileNavigation: userId为空，忽略")
            return
        }

        print("[PersonalHomepageViewController] handleUserProfileNavigation: 收到跳转通知，userId=\(userId)")

        // 如果点击的是当前用户，不需要跳转
        if userId == self.userId {
            print("[PersonalHomepageViewController] handleUserProfileNavigation: 点击的是当前用户，忽略跳转")
            return
        }

        print("[PersonalHomepageViewController] handleUserProfileNavigation: 开始跳转到个人主页")

        // 创建新的个人主页控制器
        let personalVC = PersonalHomepageViewController()
        personalVC.userId = userId

        // 推送到导航栈
        navigationController?.pushViewController(personalVC, animated: true)
    }

    /// 处理用户信息更新通知
    @objc private func handleUserInfoUpdated() {
        print("[DEBUG] 个人主页收到用户信息更新通知，刷新数据")
        fetchPersonalHomeInfo()
        // 同时重新获取各个tab的总数
        fetchAllTabCounts()
    }

    /// 处理私密账号通知 - 隐藏统计数据
    @objc private func handlePrivateAccountStats() {
        print("[DEBUG] 收到私密账号通知，隐藏统计数据")
        headerView?.hideStatsForPrivateAccount()
    }

    /// 处理公开账号通知 - 显示统计数据
    @objc private func handlePublicAccountStats() {
        print("[DEBUG] 收到公开账号通知，显示统计数据")
        headerView?.showStatsForPublicAccount()
    }

    /// 处理作品数量更新通知
    @objc private func handleWorksCountUpdate(_ notification: Notification) {
        guard let count = notification.object as? Int else { return }
        print("[DEBUG] 收到作品数量更新通知: \(count)")
        // 更新本地总数并刷新显示
        worksTotal = count
        updateSegmentTitle(for: 0, count: worksTotal)
    }

    /// 处理喜欢数量更新通知
    @objc private func handleLikeCountUpdate(_ notification: Notification) {
        guard let count = notification.object as? Int else { return }
        print("[DEBUG] 收到喜欢数量更新通知: \(count)")
        // 更新本地总数并刷新显示
        likeTotal = count
        updateSegmentTitle(for: 1, count: likeTotal)
    }

    /// 处理收藏数量更新通知
    @objc private func handleCollectCountUpdate(_ notification: Notification) {
        guard let count = notification.object as? Int else { return }
        print("[DEBUG] 收到收藏数量更新通知: \(count)")
        // 更新本地总数并刷新显示
        collectTotal = count
        updateSegmentTitle(for: 2, count: collectTotal)
    }

    /// 更新指定tab的标题
    private func updateSegmentTitle(for index: Int, count: Int) {
        let titles = ["作品", "喜欢", "收藏"]
        guard index >= 0 && index < titles.count else { return }

        var currentTitles = headerView?.segmentView.getCurrentTitles() ?? titles
        if currentTitles.count != titles.count {
            currentTitles = titles
        }

        let baseTitle = titles[index]
        currentTitles[index] = "\(baseTitle) \(formatCount(count))"

        headerView?.segmentView.setSegmentTitles(currentTitles)
        print("[DEBUG] 更新segment标题[\(index)]: \(currentTitles[index])")
    }

    /// 获取个人主页信息
    private func fetchPersonalHomeInfo() {
        guard !userId.isEmpty else { return }
        APIManager.shared.getPersonHomeInfo(customerId: userId) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let info):
                    self.personHomeInfo = info
                    // 数据获取成功后绑定到headerView
                    if let data = info.data {
                        self.headerView?.bind(data)
                        self.navTitleLabel.text = data.nickName.isEmpty ? "用户名" : data.nickName
                        // 确保关注按钮的交互性
                        self.headerView?.ensureFollowButtonInteraction()

                        // 数据绑定完成后，刷新collection view布局以适应动态高度
                        DispatchQueue.main.async {
                            self.collectionView.collectionViewLayout.invalidateLayout()
                            // 重新创建coordinator以使用新的headerViewHeight
                            self.setupCoordinator()
                        }
                    }
                    print("个人主页信息获取成功: \(info)")
                case .failure(let error):
                    // TODO: 处理错误，弹窗或提示
                    print("获取个人主页信息失败: \(error.localizedDescription)")
                }
            }
        }
    }

    /// 获取各个tab的实际总数
    func fetchAllTabCounts() {
        guard !userId.isEmpty else { return }

        let group = DispatchGroup()

        // 获取作品总数
        group.enter()
        APIManager.shared.getPersonHomeWorks(customerId: userId, page: 0, size: 1) { [weak self] result in
            defer { group.leave() }
            if case .success(let response) = result {
                self?.worksTotal = response.data?.total ?? 0
                print("[DEBUG] 作品总数: \(self?.worksTotal ?? 0)")
            }
        }

        // 获取喜欢总数
        group.enter()
        APIManager.shared.getPersonHomeWorksLike(customerId: userId, page: 0, size: 1) { [weak self] result in
            defer { group.leave() }
            if case .success(let response) = result {
                self?.likeTotal = response.data?.total ?? 0
                print("[DEBUG] 喜欢总数: \(self?.likeTotal ?? 0)")
            }
        }

        // 获取收藏总数
        group.enter()
        APIManager.shared.doWorksLikeAndCollect(customerId: userId, page: 0, size: 1) { [weak self] result in
            defer { group.leave() }
            if case .success(let response) = result {
                self?.collectTotal = response.data?.total ?? 0
                print("[DEBUG] 收藏总数: \(self?.collectTotal ?? 0)")
            }
        }

        // 所有请求完成后更新UI
        group.notify(queue: .main) { [weak self] in
            self?.updateSegmentTitlesWithActualCounts()
        }
    }

    /// 使用实际总数更新segment标题
    private func updateSegmentTitlesWithActualCounts() {
        let worksTitle = "作品 \(formatCount(worksTotal))"
        let likeTitle = "喜欢 \(formatCount(likeTotal))"
        let collectTitle = "收藏 \(formatCount(collectTotal))"

        headerView?.segmentView.setSegmentTitles([worksTitle, likeTitle, collectTitle])
        print("[DEBUG] 更新segment标题 - 作品:\(worksTotal), 喜欢:\(likeTotal), 收藏:\(collectTotal)")
    }

    /// 数字格式化方法
    private func formatCount(_ count: Int) -> String {
        if count >= 10000 {
            let value = Double(count) / 10000.0
            return String(format: "%.1f万", value)
        } else {
            return "\(count)"
        }
    }
    
    func setupUI() {
        let flowLayout = UICollectionViewFlowLayout()
        collectionView = PageScrollView(frame: CGRect.zero, collectionViewLayout: flowLayout)
        collectionView.register(PageHeaderView.self, forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: PageHeaderView.description())
        collectionView.register(UICollectionViewCell.self, forCellWithReuseIdentifier: "CellId")
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.showsVerticalScrollIndicator = false
        view.addSubview(collectionView)
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        collectionView.leftAnchor.constraint(equalTo: view.leftAnchor).isActive = true
        collectionView.topAnchor.constraint(equalTo: view.topAnchor).isActive = true
        collectionView.rightAnchor.constraint(equalTo: view.rightAnchor).isActive = true
        collectionView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor).isActive = true
        
        if #available(iOS 11.0, *) {
            collectionView.contentInsetAdjustmentBehavior = .never
        } else {
            automaticallyAdjustsScrollViewInsets = false
        }
        
        setupNavigationBar()
        
        cvcContentView = CollectionViewCellContentView()
        cvcContentView.hostScrollView = collectionView
        
        initSubViewController()
    }
    
    // 设置自定义导航栏
    private func setupNavigationBar() {
        // 返回按钮
        let returnBtn = UIButton(type: .custom)
        returnBtn.setImage(UIImage(named: "nav_back"), for: .normal)
        returnBtn.addTarget(self, action: #selector(backAction), for: .touchUpInside)
        view.addSubview(returnBtn)
        returnBtn.translatesAutoresizingMaskIntoConstraints = false
        returnBtn.leftAnchor.constraint(equalTo: view.leftAnchor, constant: 12).isActive = true
        returnBtn.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 6).isActive = true
        
        // 导航栏视图
        navigationView = UIView()
        navigationView.backgroundColor = .white
        navigationView.isHidden = true
        view.insertSubview(navigationView, belowSubview: returnBtn)
        navigationView.translatesAutoresizingMaskIntoConstraints = false
        navigationView.leftAnchor.constraint(equalTo: view.leftAnchor).isActive = true
        navigationView.topAnchor.constraint(equalTo: view.topAnchor).isActive = true
        navigationView.rightAnchor.constraint(equalTo: view.rightAnchor).isActive = true
        navigationView.heightAnchor.constraint(equalToConstant: navigationViewHeight).isActive = true
        
        // 标题
        navTitleLabel = UILabel()
        navTitleLabel.text = "用户名"
        navTitleLabel.font = .systemFont(ofSize: 18)
        navTitleLabel.textColor = UIColor.black
        navigationView.addSubview(navTitleLabel)
        navTitleLabel.translatesAutoresizingMaskIntoConstraints = false
        navTitleLabel.centerXAnchor.constraint(equalTo: navigationView.centerXAnchor).isActive = true
        navTitleLabel.centerYAnchor.constraint(equalTo: returnBtn.centerYAnchor).isActive = true
        
        // 右侧更多按钮（保持始终可见，不随导航栏透明度变化）
        moreButton = UIButton(type: .custom)
        moreButton.setImage(UIImage(named: "icon_more_black"), for: .normal)
        moreButton.addTarget(self, action: #selector(moreButtonTapped), for: .touchUpInside)
        view.addSubview(moreButton)
        moreButton.translatesAutoresizingMaskIntoConstraints = false
        moreButton.rightAnchor.constraint(equalTo: view.rightAnchor, constant: -12).isActive = true
        moreButton.centerYAnchor.constraint(equalTo: returnBtn.centerYAnchor).isActive = true
        
        // 添加导航栏底部分割线
        let separatorLine = UIView()
        separatorLine.backgroundColor = UIColor(white: 0, alpha: 0.1) // 半透明黑色
        navigationView.addSubview(separatorLine)
        separatorLine.translatesAutoresizingMaskIntoConstraints = false
        separatorLine.leftAnchor.constraint(equalTo: navigationView.leftAnchor).isActive = true
        separatorLine.rightAnchor.constraint(equalTo: navigationView.rightAnchor).isActive = true
        separatorLine.bottomAnchor.constraint(equalTo: navigationView.bottomAnchor).isActive = true
        separatorLine.heightAnchor.constraint(equalToConstant: 0.3).isActive = true
    }
    
    func setupCoordinator() {
        let segHeight = DefaultSegmentViewHeight
        
        coordinator = NestedScrollCoordinator(
            navigationViewHeight: self.navigationViewHeight,
            headerViewHeight: self.headerViewHeight,
            segmentViewHeight: segHeight
        )
        coordinator.delegate = self
    }
    
    @objc func backAction() {
        if let nav = navigationController {
            if nav.viewControllers.count > 1 {
                nav.popViewController(animated: true)
            } else if nav.presentingViewController != nil {
                nav.dismiss(animated: true)
            } else {
                // fallback
                self.dismiss(animated: true)
            }
        } else {
            self.dismiss(animated: true)
        }
    }
    
    // MARK: - More Button Action
    @objc private func moreButtonTapped() {
        let actionSheet = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)

        let shareAction = UIAlertAction(title: "分享", style: .default) { _ in
            print("分享按钮被点击")
            let shareVC = UserSharingViewController(userId: self.userId)
            self.navigationController?.pushViewController(shareVC, animated: true)
        }

        let blockAction = UIAlertAction(title: "拉黑", style: .destructive) { [weak self] _ in
            guard let self = self else { return }
            let title = "将 \"\(self.navTitleLabel.text ?? "用户")\" 拉黑"
            let message = "拉黑后，对方将无法查看你的作品，也无法与你互动。对方不会收到被拉黑的通知。"
            self.presentCustomAlert(title: title, message: message, leftTitle: "取消", rightTitle: "拉黑") { [weak self] in
                guard let self = self else { return }
                APIManager.shared.doUserBlock(customerId: self.userId) { result in
                    DispatchQueue.main.async {
                        switch result {
                        case .success:
                            print("拉黑成功")
                            // HUD 提示
                            self.showToast("拉黑成功")
                            // 可按需刷新UI
                        case .failure(let error):
                            print("拉黑失败: \(error.localizedDescription)")
                        }
                    }
                }
            }
        }

        let cancelAction = UIAlertAction(title: "取消", style: .cancel, handler: nil)

        actionSheet.addAction(shareAction)
        actionSheet.addAction(blockAction)
        actionSheet.addAction(cancelAction)

        // iPad 兼容处理
        if let popover = actionSheet.popoverPresentationController {
            popover.sourceView = moreButton
            popover.sourceRect = moreButton.bounds
        }

        present(actionSheet, animated: true, completion: nil)
    }
    
    func initSubViewController() {
        let worksVC = VideoListController(type: .works, userId: userId)
        let likeVC = VideoListController(type: .like, userId: userId)
        let collectVC = VideoListController(type: .collect, userId: userId)
        childVCs = [worksVC as PageContainScrollView, likeVC as PageContainScrollView, collectVC as PageContainScrollView]
        childVCs.forEach { vc in
            addChild(vc)
            vc.scrollViewDidScroll(callBack: { [weak self] scrollview in
                self?.coordinator.contentScrollViewDidScroll(scrollview)
            })
        }
    }
    
    // 在合适位置新增统一API请求方法
    private func requestFollowUser(type: Int, alert: CommonAlertView?) {
        APIManager.shared.followUser‌(customerId: self.userId, type: type, worksId: 0) { [weak self] result in
            guard let self = self else { return }
            if case .success = result {
                alert?.dismiss()
                self.fetchPersonalHomeInfo()
                // 关注/取关成功后，重新获取各个tab的总数以更新显示
                self.fetchAllTabCounts()
            } else {
                // 失败提示，可根据需要弹窗
            }
        }
    }
    
    // MARK: - 自定义弹窗
    private func presentCustomAlert(title: String,
                                    message: String? = nil,
                                    leftTitle: String = "取消",
                                    rightTitle: String = "确定",
                                    confirmHandler: @escaping () -> Void) {
        let alert = CommonAlertView(title: title,
                                    message: message ?? "",
                                    leftButtonTitle: leftTitle,
                                    rightButtonTitle: rightTitle)
        alert.onLeftButtonTap = {
            alert.dismiss()
        }
        alert.onRightButtonTap = {
            alert.dismiss()
            confirmHandler()
        }
        alert.show()
    }
}

extension PersonalHomepageViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(_: UICollectionView, layout _: UICollectionViewLayout, referenceSizeForHeaderInSection _: Int) -> CGSize {
        return CGSize(width: view.width, height: headerViewHeight)
    }
    
    func collectionView(_: UICollectionView, layout _: UICollectionViewLayout, sizeForItemAt _: IndexPath) -> CGSize {
        let actualSegmentHeight = DefaultSegmentViewHeight
        return CGSize(width: view.width, height: view.height - navigationViewHeight - actualSegmentHeight)
    }
}

extension PersonalHomepageViewController: UICollectionViewDataSource {
    func collectionView(_: UICollectionView, numberOfItemsInSection _: Int) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "CellId", for: indexPath)
        cvcContentView.delegate = self
        cell.contentView.addSubview(cvcContentView)
        cvcContentView.frame = cell.contentView.bounds
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind _: String, at indexPath: IndexPath) -> UICollectionReusableView {
        headerView = collectionView.dequeueReusableSupplementaryView(ofKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: PageHeaderView.description(), for: indexPath) as? PageHeaderView
        headerView?.segmentView.delegate = self
        // 新增：如果已有数据，立即绑定
        if let data = personHomeInfo?.data {
            headerView?.bind(data)
            // 数据绑定完成后，刷新布局
            DispatchQueue.main.async {
                self.collectionView.collectionViewLayout.invalidateLayout()
            }
        }

        // 确保关注按钮的交互性
        headerView?.ensureFollowButtonInteraction()
        // 新增：关注按钮回调
        headerView?.onFollowButtonTap = { [weak self] isFollowing in
            guard let self = self else { return }
            print("关注按钮回调被触发，当前状态：\(isFollowing ? "已关注" : "未关注")") // 添加调试日志
            if isFollowing {
                // 弹窗确认
                let alert = CommonAlertView(
                    title: "不再关注作者？",
                    message: "",
                    leftButtonTitle: "取消",
                    rightButtonTitle: "不再关注"
                )
                alert.onLeftButtonTap = {
                    alert.dismiss()
                }
                alert.onRightButtonTap = {
                    self.requestFollowUser(type: 2, alert: alert)
                }
                alert.show(in: self.view)
            } else {
                // 直接关注
                self.requestFollowUser(type: 1, alert: nil)
            }
        }
        return headerView!
    }
}

extension PersonalHomepageViewController: CollectionViewCellContentViewDataSource {
    func collectionViewScroll(progress: CGFloat, sourceIndex: Int, targetIndex: Int) {
        headerView?.segmentView.setTitle(progress: progress, sourceIndex: sourceIndex, targetIndex: targetIndex)
    }
    
    func numberOfViewController() -> Int {
        return childVCs.count
    }
    
    func viewController(itemAt indexPath: IndexPath) -> UIViewController {
        return childVCs[indexPath.item]
    }
}

extension PersonalHomepageViewController: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        if scrollView === collectionView {
            coordinator.hostScrollViewDidScroll(scrollView)

            // 额外保障：在每次滚动时强制刷新蒙版覆盖
            headerView?.forceRefreshOverlay()
        }
    }
}

extension PersonalHomepageViewController: NestedScrollCoordinatorDelegate {
    func scrollStateDidChange(isHostScrollEnabled: Bool, isContentScrollEnabled: Bool) {
        // 不再动态设置滚动条显示
        // collectionView.showsVerticalScrollIndicator = isHostScrollEnabled
    }
    
    func setHostScrollOffset(_ offset: CGPoint) {
        if collectionView.contentOffset != offset {
            collectionView.contentOffset = offset
        }
    }
    
    func setContentScrollOffset(_ offset: CGPoint, for scrollView: UIScrollView) {
        if scrollView.contentOffset != offset {
            scrollView.contentOffset = offset
        }
    }
    
    func updateNavigationView(alpha: CGFloat, isHidden: Bool) {
        navigationView.alpha = alpha
        navigationView.isHidden = isHidden
        // 当导航栏完全透明或被隐藏时，不再拦截触摸，避免遮挡 header 内的控件
        // 增加更严格的条件：只有当导航栏完全不透明时才启用交互
        navigationView.isUserInteractionEnabled = !isHidden && alpha > 0.8

        // 调试日志
//        print("导航栏状态更新 - alpha: \(alpha), isHidden: \(isHidden), isUserInteractionEnabled: \(navigationView.isUserInteractionEnabled)")
    }
    
    func animateHeaderBackground(offset: CGFloat) {
        headerView?.backgroundImageAnimation(offset: offset)
        // 额外保障：确保蒙版始终可见
        headerView?.ensureGradientOverlayVisible()
    }
    
    func currentHostScrollViewContentOffset() -> CGPoint {
        return collectionView.contentOffset
    }
}

extension PersonalHomepageViewController: PageSegmentViewDelegate {
    func pageSegment(selectedIndex index: Int) {
        cvcContentView.switchPage(index: index)
    }
}
